export const Endpoints = {
  join_meeting: {
    url: "/rtc/meeting/join",
    method: "POST",
  },
  remove_participant: {
    url: "/rtc/meeting/remove/participant",
    method: "POST",
  },
  verify_password: {
    url: "/meeting/verify/password",
    method: "POST",
  },
  mute_participant: {
    url: "/rtc/meeting/mute/participant",
    method: "POST",
  },
  end_meeting: {
    url: "/rtc/meeting/delete",
    method: "POST",
  },
  recording_start: {
    url: "/rtc/meeting/recording/start",
    method: "POST",
  },
  recording_stop: {
    url: "/rtc/meeting/recording/stop",
    method: "POST",
  },
  livestream_start: {
    url: "/rtc/meeting/livestreaming/start",
    method: "POST",
  },
  livestream_stop: {
    url: "/rtc/meeting/livestreaming/stop",
    method: "POST",
  },
  get_meeting_details: (id) => ({
    url: `/rtc/meeting/detail?meeting_uid=${id}`,
    method: "GET",
  }),
  report_problem: {
    url: "/rtc/meeting/create/report",
    method: "POST",
  },
  add_participant_to_lobby: {
    url: "/rtc/meeting/addParticipant/toLobby",
    method: "POST",
  },
  status_update_participant_lobby: {
    url: "/rtc/meeting/update/participantLobbyStatus",
    method: "PUT",
  },
  assign_co_host: {
    url: "/rtc/meeting/create/cohost",
    method: "POST",
  },
  get_breakout_room_token: {
    url: "/rtc/meeting/breakoutRoom/create",
    method: "POST",
  },
  get_breakout_room_detail: (id) => ({
    url: `/rtc/meeting/breakoutRoom/roomsDetail?meeting_uid=${id}`,
    method: "GET",
  }),
  move_participant_to_breakout_room: {
    url: "/rtc/meeting/breakoutRoom/moveParticipant",
    method: "POST",
  },
  get_breakout_room_config: (id) => ({
    url: `/rtc/meeting/breakoutRoom/getSettings?meeting_uid=${id}`,
    method: "GET",
  }),
  save_breakout_room_config: {
    url: "/rtc/meeting/breakoutRoom/saveSettings",
    method: "POST",
  },
  start_live_transcription: {
    url: "/rtc/meeting/transcription/start",
    method: "POST",
  },
  get_meeting_features: (id) => ({
    url: `/rtc/meeting/availableFeatures?subscription_id=${id}`,
    method: "GET",
  }),
  setTranscriptionDetail: {
    url: "/rtc/meeting/update/transcriptionLanguage",
    method: "POST",
  },
  meetingTranslateText: {
    url: "/rtc/meeting/text/translation",
    method: "POST",
  },
  get_virtual_background: {
    url: "/rtc/meeting/virtualBackground",
    method: "GET",
  },
  set_virtual_background: {
    url: "/rtc/meeting/uploadVirtualBackground",
    method: "POST",
  },
  delete_virtual_background: {
    url: "/rtc/meeting/delete/virtualBackground",
    method: "DELETE",
  },
  common_password_verify: {
    url: "/rtc/meeting/verify/commonPassword",
    method: "POST",
  },
  send_chat_attachment: {
    url: "/rtc/meeting/chat/uploadAttachment",
    method: "POST",
  },
  sip_connection: {
    url: "/rtc/meeting/sip/connection",
    method: "POST",
  },
  get_single_whiteboard_data: (id)=>({
    url:`/rtc/meeting/whiteboard/getDetail?whiteboard_id=${id}`,
    method:"GET"
  }),
  get_multiple_whiteboard_data: (id)=>({
    url:`/rtc/meeting/whiteboard/get?meeting_id=${id}`,
    method:"GET"
  }),
  save_whiteboard_data: {
    url:"/rtc/meeting/whiteboard/save",
    method:"POST"
  },
  update_whiteboard_data: {
    url:"/rtc/meeting/whiteboard/update",
    method:"PUT"
  },
  update_whiteboard_status:{
    url:"/rtc/meeting/whiteboard/updateStatus",
    method:"PUT"
  },
  getTranscriptionDetail:{
    url:"/api/translator/translate/text",
    method:"GET"
  },
  participant_name_update: {
    url: "/rtc/meeting/updateParticipant/name",
    method:"POST"
  },
  dispatchAgent: () => ({
    url: `/rtc/meeting/dispatch/agent`,
    method: "POST",
  }),
  deDispatchAgent: () => ({
    url: `/rtc/meeting/dedispatch/agent`,
    method: "POST",
  }),
};
