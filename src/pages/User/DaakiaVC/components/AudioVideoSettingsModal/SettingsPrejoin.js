/* eslint-disable */
import React, { useState, useEffect, useRef } from "react";
import { Modal, Tabs } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import { useMediaQuery } from "../../hooks/useMediaQuerry";
import { ReactComponent as BlueMicIcon } from "./Assets/bluemic.svg";
import { ReactComponent as BlueVideoIcon } from "./Assets/bluevid.svg";
import { ReactComponent as GreyMicIcon } from "./Assets/greymic.svg";
import { ReactComponent as GreyVideoIcon } from "./Assets/greyvid.svg";
import soundTestAudio from "./Assets/soundtest.mp3";

// Import extracted components
import {
  MicrophoneSettings,
  SpeakerSettings,
  CameraSettings,
  AudioEnhancementSettings,
  VideoEnhancementSettings
} from "./components/settingsPrejoinModal";

import "./SettingsPrejoin.scss";

export default function SettingsPrejoin({
  open,
  setOpen,
  // Audio props
  audioDeviceId,
  setAudioDeviceId,
  audioTrack,
  audioEnabled,
  setAudioEnabled,
  // Video props
  videoDeviceId,
  setVideoDeviceId,
  // Speaker props
  speakerDeviceId,
  setSpeakerDeviceId,
  // Settings props
  room,
  isSelfVideoMirrored,
  setIsSelfVideoMirrored,
  brightness = 100,
  onBrightnessChange,
  // Volume props (following brightness pattern, no RPC needed)
  outputVolume = 100,
  onOutputVolumeChange,
  // Auto video off props (following volume pattern, no RPC needed)
  autoVideoOff = false,
  onAutoVideoOffChange,
  // Auto audio off props (following volume pattern, no RPC needed)
  autoAudioOff = false,
  onAutoAudioOffChange,
  // Permission props
  permissions = { camera: false, microphone: false },
  // Virtual Background Modal props
  setIsVisualEffectsModalOpen,
}) {
  // Responsive breakpoints
  const isMobile = useMediaQuery("(max-width: 767px)");
  const isTabletOrBelow = useMediaQuery("(max-width: 1023px)");
  const isLargeScreen = useMediaQuery("(min-width: 1024px)");

  // Active tab state
  const [activeTab, setActiveTab] = useState("audio");

  // Device states
  const [audioDevices, setAudioDevices] = useState([]);
  const [videoDevices, setVideoDevices] = useState([]);
  const [speakerDevices, setSpeakerDevices] = useState([]);

  // Audio level states
  const [speakerAudioLevel, setSpeakerAudioLevel] = useState(0);

  // Gain node for volume control
  const gainNodeRef = useRef(null);

  // Simplified audio level state - single source of truth
  const [currentAudioLevel, setCurrentAudioLevel] = useState(0);

  // Test mic states
  const [isMicTesting, setIsMicTesting] = useState(false);
  const [micOriginalState, setMicOriginalState] = useState(null); // Store original mic state

  // Test speaker states
  const [isSpeakerTesting, setIsSpeakerTesting] = useState(false);
  const speakerTestAudioRef = useRef(null);

  // Audio settings states
  const [noiseCancellation, setNoiseCancellation] = useState(
    room?.options?.audioCaptureDefaults?.noiseSuppression || false
  );
  const [echoCancellation, setEchoCancellation] = useState(
    room?.options?.audioCaptureDefaults?.echoCancellation || false
  );
  const [autoMuteOnJoin, setAutoMuteOnJoin] = useState(true);

  // Single audio monitoring system
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const animationFrameRef = useRef(null);
  const testTimeoutRef = useRef(null);

  // Speaker audio monitoring system
  const speakerAudioContextRef = useRef(null);
  const speakerAnalyserRef = useRef(null);
  const speakerAnimationFrameRef = useRef(null);

  // Scrollbar visibility refs
  const scrollTimeoutRef = useRef(null);

  // Fetch available devices
  useEffect(() => {
    const fetchDevices = async () => {
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();

        const audioInputs = devices.filter(device => device.kind === 'audioinput');
        const videoInputs = devices.filter(device => device.kind === 'videoinput');
        const audioOutputs = devices.filter(device => device.kind === 'audiooutput');

        setAudioDevices(audioInputs);
        setVideoDevices(videoInputs);
        setSpeakerDevices(audioOutputs);

        // Set default speaker if none selected and devices are available
        if (audioOutputs.length > 0 && !speakerDeviceId && setSpeakerDeviceId) {
          setSpeakerDeviceId(audioOutputs[0].deviceId);
        }
      } catch (error) {
        console.error('Error fetching devices:', error);
      }
    };

    if (open && (permissions.camera || permissions.microphone)) {
      fetchDevices();
    }
  }, [open, permissions, speakerDeviceId, setSpeakerDeviceId]);



  // Unified audio level monitoring - ONLY during explicit testing
  useEffect(() => {
    const shouldMonitor = open && audioTrack && isMicTesting; // Only monitor during explicit testing

    if (!shouldMonitor) {
      // Clean up and reset level when not monitoring
      setCurrentAudioLevel(0);
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext:', error);
        }
        audioContextRef.current = null;
      }
      return;
    }

    const setupAudioMonitoring = async () => {
      try {
        // Clean up existing context first
        if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
          try {
            audioContextRef.current.close();
          } catch (error) {
            console.warn('Error closing existing AudioContext:', error);
          }
        }
        audioContextRef.current = null;

        const stream = audioTrack.mediaStream;
        if (!stream) return;

        audioContextRef.current = new AudioContext();
        const source = audioContextRef.current.createMediaStreamSource(stream);
        analyserRef.current = audioContextRef.current.createAnalyser();

        // Optimized settings for audio monitoring
        analyserRef.current.fftSize = 2048;
        analyserRef.current.smoothingTimeConstant = isMicTesting ? 0.1 : 0.3; // More responsive during testing
        source.connect(analyserRef.current);

        const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);

        const updateLevel = () => {
          if (!analyserRef.current || !audioContextRef.current || audioContextRef.current.state === 'closed') {
            return;
          }

          analyserRef.current.getByteTimeDomainData(dataArray);

          // Calculate RMS (Root Mean Square) for audio level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i += 1) {
            const sample = (dataArray[i] - 128) / 128;
            sum += sample * sample;
          }
          const rms = Math.sqrt(sum / dataArray.length);

          // Convert to percentage with scaling
          let level = rms * 100 * (isMicTesting ? 8 : 6); // Higher sensitivity during testing

          // Apply logarithmic scaling for more natural response
          if (level > 0) {
            level = Math.log10(level + 1) * (isMicTesting ? 50 : 45);
          }

          level = Math.min(100, Math.max(0, level));
          setCurrentAudioLevel(level);

          // Continue monitoring if conditions are still met - ONLY during testing
          const stillShouldMonitor = open && audioTrack && isMicTesting;
          if (stillShouldMonitor) {
            animationFrameRef.current = requestAnimationFrame(updateLevel);
          }
        };

        updateLevel();
      } catch (error) {
        console.error('Error setting up audio monitoring:', error);
        setCurrentAudioLevel(0);
      }
    };

    setupAudioMonitoring();

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
        animationFrameRef.current = null;
      }
      if (audioContextRef.current && audioContextRef.current.state !== 'closed') {
        try {
          audioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing AudioContext during cleanup:', error);
        }
        audioContextRef.current = null;
      }
    };
  }, [open, audioTrack, isMicTesting]); // Removed audioEnabled dependency

  // Speaker audio level monitoring
  useEffect(() => {
    const shouldMonitorSpeaker = isSpeakerTesting && speakerTestAudioRef.current;

    if (!shouldMonitorSpeaker) {
      // Clean up and reset speaker level when not monitoring
      setSpeakerAudioLevel(0);
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext:', error);
        }
        speakerAudioContextRef.current = null;
      }
      return;
    }

    const setupSpeakerAudioMonitoring = async () => {
      try {
        // Clean up existing context first
        if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
          try {
            speakerAudioContextRef.current.close();
          } catch (error) {
            console.warn('Error closing existing speaker AudioContext:', error);
          }
        }
        speakerAudioContextRef.current = null;

        const audio = speakerTestAudioRef.current;
        if (!audio) return;

        // Try to set up audio monitoring, but don't break audio playback if it fails
        try {
          speakerAudioContextRef.current = new AudioContext();
          const source = speakerAudioContextRef.current.createMediaElementSource(audio);
          speakerAnalyserRef.current = speakerAudioContextRef.current.createAnalyser();

          // Connect source to analyser and then to destination
          source.connect(speakerAnalyserRef.current);
          speakerAnalyserRef.current.connect(speakerAudioContextRef.current.destination);

          // Optimized settings for speaker audio monitoring
          speakerAnalyserRef.current.fftSize = 2048;
          speakerAnalyserRef.current.smoothingTimeConstant = 0.2;
        } catch (audioError) {
          console.warn('Failed to set up speaker audio monitoring, continuing without level detection:', audioError);
          // Clean up failed attempt
          if (speakerAudioContextRef.current) {
            try {
              speakerAudioContextRef.current.close();
            } catch (e) {
              // Ignore cleanup errors
            }
            speakerAudioContextRef.current = null;
          }
          return; // Exit early, audio will play normally without monitoring
        }

        const dataArray = new Uint8Array(speakerAnalyserRef.current.frequencyBinCount);

        const updateSpeakerLevel = () => {
          if (!speakerAnalyserRef.current || !speakerAudioContextRef.current || speakerAudioContextRef.current.state === 'closed') {
            return;
          }

          try {
            speakerAnalyserRef.current.getByteFrequencyData(dataArray);
          } catch (error) {
            console.warn('Error getting frequency data:', error);
            return;
          }

          // Calculate average frequency data for speaker level
          let sum = 0;
          for (let i = 0; i < dataArray.length; i += 1) {
            sum += dataArray[i];
          }
          const average = sum / dataArray.length;

          // Convert to percentage with higher scaling for better sensitivity
          let level = (average / 255) * 100 * 4; // Increased from 1.5 to 4 for better visibility

          // Apply additional boost for low levels
          if (level > 0) {
            level = (level / 100) ** 0.6 * 100; // Power curve for better response
          }

          level = Math.min(100, Math.max(0, level));
          setSpeakerAudioLevel(level);

          // Continue monitoring if conditions are still met
          const stillShouldMonitor = isSpeakerTesting && speakerTestAudioRef.current;
          if (stillShouldMonitor) {
            speakerAnimationFrameRef.current = requestAnimationFrame(updateSpeakerLevel);
          }
        };

        updateSpeakerLevel();
      } catch (error) {
        console.error('Error setting up speaker audio monitoring:', error);
        setSpeakerAudioLevel(0);
      }
    };

    // Small delay to ensure audio element is ready
    setTimeout(setupSpeakerAudioMonitoring, 100);

    return () => {
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext during cleanup:', error);
        }
        speakerAudioContextRef.current = null;
      }
    };
  }, [isSpeakerTesting]);

  // Handle scrollbar visibility
  useEffect(() => {
    const handleScroll = (event) => {
      const element = event.target;
      element.classList.add('scrolling');

      if (scrollTimeoutRef.current) {
        clearTimeout(scrollTimeoutRef.current);
      }

      scrollTimeoutRef.current = setTimeout(() => {
        element.classList.remove('scrolling');
      }, 1000);
    };

    if (open) {
      const scrollableElements = document.querySelectorAll('.ant-tabs-tabpane, .settings-content');

      scrollableElements.forEach(element => {
        element.addEventListener('scroll', handleScroll, { passive: true });
      });

      return () => {
        scrollableElements.forEach(element => {
          element.removeEventListener('scroll', handleScroll);
        });

        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [open]);

  // Simplified test microphone function
  const testMicrophone = async () => {
    if (isMicTesting) {
      // Stop testing
      setIsMicTesting(false);

      // Clear test timeout
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original microphone state if we changed it
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      return;
    }

    // Start testing
    if (!audioEnabled) {
      setMicOriginalState(false); // Remember it was off
      setAudioEnabled(true);

      // Wait a bit for the audio track to be available
      setTimeout(() => {
        if (audioTrack) {
          setIsMicTesting(true);
        }
      }, 500);
    } else {
      // Mic was already on, just start testing
      setMicOriginalState(true); // Remember it was on
      setIsMicTesting(true);
    }

    // Auto-stop test after 10 seconds
    testTimeoutRef.current = setTimeout(() => {
      if (isMicTesting) {
        setIsMicTesting(false);

        // Restore original state
        if (micOriginalState !== null) {
          setAudioEnabled(micOriginalState);
          setMicOriginalState(null);
        }
      }
    }, 10000);
  };

  // Clean up when modal closes
  useEffect(() => {
    if (!open) {
      // Stop any ongoing test
      setIsMicTesting(false);

      // Clear timeouts
      if (testTimeoutRef.current) {
        clearTimeout(testTimeoutRef.current);
        testTimeoutRef.current = null;
      }

      // Restore original mic state if needed
      if (micOriginalState !== null) {
        setAudioEnabled(micOriginalState);
        setMicOriginalState(null);
      }

      // Stop speaker test and cleanup
      if (speakerTestAudioRef.current) {
        speakerTestAudioRef.current.pause();
        speakerTestAudioRef.current.currentTime = 0;
      }
      setIsSpeakerTesting(false);

      // Cleanup speaker audio monitoring
      if (speakerAnimationFrameRef.current) {
        cancelAnimationFrame(speakerAnimationFrameRef.current);
        speakerAnimationFrameRef.current = null;
      }
      if (speakerAudioContextRef.current && speakerAudioContextRef.current.state !== 'closed') {
        try {
          speakerAudioContextRef.current.close();
        } catch (error) {
          console.warn('Error closing speaker AudioContext during modal close:', error);
        }
        speakerAudioContextRef.current = null;
      }

      // Cleanup gain node
      if (gainNodeRef.current) {
        gainNodeRef.current.disconnect();
        gainNodeRef.current = null;
      }

      // Reset audio levels
      setCurrentAudioLevel(0);
      setSpeakerAudioLevel(0);
    }
  }, [open]);

  // Handle device changes
  const handleAudioDeviceChange = (deviceId) => {
    setAudioDeviceId(deviceId);
  };

  const handleVideoDeviceChange = (deviceId) => {
    setVideoDeviceId(deviceId);
  };

  const handleSpeakerDeviceChange = (deviceId) => {
    if (setSpeakerDeviceId) {
      setSpeakerDeviceId(deviceId);
    }
  };

  // Handle audio settings changes
  const handleNoiseCancellation = (checked) => {
    setNoiseCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.noiseSuppression = checked;
    }
  };

  const handleEchoCancellation = (checked) => {
    setEchoCancellation(checked);
    if (room?.options?.audioCaptureDefaults) {
      room.options.audioCaptureDefaults.echoCancellation = checked;
    }
  };

  // Handle output volume changes - control system volume and test audio
  const handleOutputVolumeChange = async (value) => {
    // Call the parent component's volume change handler (following brightness pattern)
    if (onOutputVolumeChange) {
      onOutputVolumeChange(value);
    }

    // Update test audio volume in real-time if it's playing
    if (isSpeakerTesting && speakerTestAudioRef.current) {
      speakerTestAudioRef.current.volume = value / 100;
    }

    try {
      // Try to control system volume using Web Audio API
      if (!audioContextRef.current) {
        if (window.AudioContext) {
          audioContextRef.current = new window.AudioContext();
        } else {
          throw new Error('Web Audio API not supported');
        }
      }

      // Create gain node if it doesn't exist
      if (!gainNodeRef.current) {
        gainNodeRef.current = audioContextRef.current.createGain();
        gainNodeRef.current.connect(audioContextRef.current.destination);
      }

      // Set the gain value (0.0 to 1.0)
      const gainValue = value / 100;
      gainNodeRef.current.gain.setValueAtTime(gainValue, audioContextRef.current.currentTime);
    } catch (error) {
      console.warn('Could not control system volume:', error);
      // Fallback: just update the state for test audio
    }
  };



  // Test functions
  const testSpeaker = async () => {
    if (isSpeakerTesting) {
      // Stop current test
      if (speakerTestAudioRef.current) {
        speakerTestAudioRef.current.pause();
        speakerTestAudioRef.current.currentTime = 0;
      }
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
      return;
    }

    try {
      // Always create a fresh audio element to avoid MediaElementSource conflicts
      speakerTestAudioRef.current = new Audio(soundTestAudio);
      speakerTestAudioRef.current.preload = 'auto';

      const audio = speakerTestAudioRef.current;

      // Set volume based on output volume setting
      audio.volume = outputVolume / 100;

      // Set the audio output device if supported and speaker is selected
      if (audio.setSinkId && speakerDeviceId) {
        try {
          await audio.setSinkId(speakerDeviceId);
        } catch (error) {
          console.warn('Failed to set audio output device:', error);
          // Continue with default output device
        }
      }

      // Set up event listeners
      const cleanup = () => {
        setIsSpeakerTesting(false);
        setSpeakerAudioLevel(0);
      };

      // Define event handlers without circular references
      const onEnded = () => {
        cleanup();
      };

      const onError = (error) => {
        console.error('Audio playback error:', error);
        cleanup();
      };

      audio.addEventListener('ended', onEnded, { once: true });
      audio.addEventListener('error', onError, { once: true });

      // Start playing
      setIsSpeakerTesting(true);
      await audio.play();
    } catch (error) {
      console.error('Failed to play speaker test audio:', error);
      setIsSpeakerTesting(false);
      setSpeakerAudioLevel(0);
    }
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Dynamic tab label component
  const TabLabel = ({ tabKey, icon: { blue: BlueIcon, grey: GreyIcon }, text }) => {
    const isActive = activeTab === tabKey;
    const IconComponent = isActive ? BlueIcon : GreyIcon;

    return (
      <div className="tab-label">
        <IconComponent />
        <span>{text}</span>
      </div>
    );
  };

  // Tab items configuration
  const tabItems = [
    {
      key: 'audio',
      label: (
        <TabLabel
          tabKey="audio"
          icon={{ blue: BlueMicIcon, grey: GreyMicIcon }}
          text="Audio"
        />
      ),
      children: (
        <div className="settings-content">
          <div className="settings-section">
            <div className="settings-header">
              <h3>Audio Settings</h3>
              <p className="settings-description">Change your audio settings here</p>
            </div>
          </div>

          {/* Microphone Section */}
          <MicrophoneSettings
            audioDevices={audioDevices}
            audioDeviceId={audioDeviceId}
            onAudioDeviceChange={handleAudioDeviceChange}
            permissions={permissions}
            onTestMicrophone={testMicrophone}
            isMicTesting={isMicTesting}
            audioEnabled={audioEnabled}
            micOriginalState={micOriginalState}
            currentAudioLevel={currentAudioLevel}
          />

          {/* Speaker Section */}
          <SpeakerSettings
            speakerDevices={speakerDevices}
            speakerDeviceId={speakerDeviceId}
            onSpeakerDeviceChange={handleSpeakerDeviceChange}
            permissions={permissions}
            onTestSpeaker={testSpeaker}
            isSpeakerTesting={isSpeakerTesting}
            speakerAudioLevel={speakerAudioLevel}
            outputVolume={outputVolume}
            onOutputVolumeChange={handleOutputVolumeChange}
          />

          {/* Audio Enhancement Section */}
          <AudioEnhancementSettings
            noiseCancellation={noiseCancellation}
            onNoiseCancellationChange={handleNoiseCancellation}
            echoCancellation={echoCancellation}
            onEchoCancellationChange={handleEchoCancellation}
            autoMuteOnJoin={autoMuteOnJoin}
            onAutoMuteOnJoinChange={setAutoMuteOnJoin}
            autoAudioOff={autoAudioOff}
            onAutoAudioOffChange={onAutoAudioOffChange}
          />
        </div>
      ),
    },
    // {
    //   key: 'video',
    //   label: (
    //     <TabLabel
    //       tabKey="video"
    //       icon={{ blue: BlueVideoIcon, grey: GreyVideoIcon }}
    //       text="Video"
    //     />
    //   ),
    //   children: (
    //     <div className="settings-content">
    //       <div className="settings-section">
    //         <div className="settings-header">
    //           <h3>Video Settings</h3>
    //           <p className="settings-description">Change your video settings here</p>
    //         </div>

    //         {/* Background & Effects Notification Banner */}
    //         <div
    //           className="background-effects-notification "
    //           onClick={() => {
    //             if (setIsVisualEffectsModalOpen) {
    //               setIsVisualEffectsModalOpen(true);
    //               setOpen(false); // Close current settings modal
    //             }
    //           }}
    //         >
    //           <div className="notification-content">
    //             <span className="notification-text">
    //               Other Video improvement filters are now available under{' '}
    //               <span className="notification-link">Background & Effects</span>.
    //             </span>
    //           </div>
    //         </div>
    //       </div>

    //       {/* Camera Section */}
    //       <CameraSettings
    //         videoDevices={videoDevices}
    //         videoDeviceId={videoDeviceId}
    //         onVideoDeviceChange={handleVideoDeviceChange}
    //         permissions={permissions}
    //       />

    //       {/* Video Enhancement Section */}
    //       <VideoEnhancementSettings
    //         isSelfVideoMirrored={isSelfVideoMirrored}
    //         onMirrorVideoChange={setIsSelfVideoMirrored}
    //         brightness={brightness}
    //         onBrightnessChange={onBrightnessChange}
    //         autoVideoOff={autoVideoOff}
    //         onAutoVideoOffChange={onAutoVideoOffChange}
    //       />
    //     </div>
    //   ),
    // },
  ];

  return (
    <Modal
      open={open}
      onOk={handleClose}
      onCancel={handleClose}
      className={`settings-prejoin-modal ${isMobile ? 'mobile-modal' : ''} ${isTabletOrBelow && !isMobile ? 'tablet-modal' : ''}`}
      footer={null}
      width={isMobile ? "100vw" : "auto"} // Full viewport width for mobile
      height={isMobile ? "100vh" : undefined}
      style={isMobile ? {
        maxWidth: '100vw',
        width: '100vw',
        margin: 0,
        padding: 0,
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        height: '100vh',
        position: 'fixed',
        transform: 'none'
      } : {
        maxWidth: '95vw', // Prevent exceeding viewport
        width: 'auto' // Auto width
      }}
      closeIcon={null}
      centered={!isMobile}
      destroyOnClose={false}
      maskClosable={!isMobile}
      title={
        <div className="custom-modal-header">
          <span className="custom-modal-title">Settings</span>
          <div className="custom-close-button" onClick={handleClose}>
            <CloseOutlined />
          </div>
        </div>
      }
    >
      <Tabs
        defaultActiveKey="audio"
        activeKey={activeTab}
        onChange={setActiveTab}
        items={tabItems}
        tabPosition={isLargeScreen ? "left" : "top"}
        className="settings-tabs"
      />
    </Modal>
  );
}