import React from "react";
import { Modal } from "antd";
import "./permissionUi.scss";
import PermissionUiImage from "../../customFabs/icons/premissionUiImage.svg";
import PermissionUiMicImage from "../../customFabs/icons/permissionUiMic.svg";

// Browser detection utilities
const isMobileBrowser = () => {
  return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
};

const getBrowserType = () => {
  const userAgent = navigator.userAgent.toLowerCase();

  // Safari detection (including iOS Safari)
  if (/safari/.test(userAgent) && !/chrome/.test(userAgent) && !/chromium/.test(userAgent)) {
    return 'safari';
  }

  // Firefox detection
  if (/firefox/.test(userAgent)) {
    return 'firefox';
  }

  // Chrome detection (includes Chromium-based browsers)
  if (/chrome/.test(userAgent) && !/edge/.test(userAgent)) {
    return 'chrome';
  }

  // Edge detection (new Chromium-based Edge)
  if (/edge/.test(userAgent) || /edg/.test(userAgent)) {
    return 'edge';
  }

  return 'unknown';
};

const isSafari = () => getBrowserType() === 'safari';
const isFirefox = () => getBrowserType() === 'firefox';

// Check if browser supports permission API
const supportsPermissionAPI = () => {
  return 'permissions' in navigator && 'query' in navigator.permissions;
};

// Check if browser requires user gesture for media access
const requiresUserGesture = () => {
  return isSafari() || isMobileBrowser();
};

// Browser-specific getUserMedia strategies
const getUserMediaWithBrowserStrategy = async (constraints) => {
  const browserType = getBrowserType();

  try {
    switch (browserType) {
      case 'chrome':
      case 'edge':
        // Chrome/Edge: Try simultaneous audio+video request
        if (constraints.audio && constraints.video) {
          return await navigator.mediaDevices.getUserMedia(constraints);
        } else {
          // Single constraint request
          return await navigator.mediaDevices.getUserMedia(constraints);
        }

      case 'firefox':
        // Firefox: Sequential requests for better compatibility
        if (constraints.audio && constraints.video) {
          try {
            // Try simultaneous first
            return await navigator.mediaDevices.getUserMedia(constraints);
          } catch (error) {
            // Fallback to sequential
            const streams = [];
            if (constraints.audio) {
              const audioStream = await navigator.mediaDevices.getUserMedia({ audio: constraints.audio });
              streams.push(audioStream);
            }
            if (constraints.video) {
              const videoStream = await navigator.mediaDevices.getUserMedia({ video: constraints.video });
              streams.push(videoStream);
            }

            // Combine streams
            const combinedStream = new MediaStream();
            streams.forEach(stream => {
              stream.getTracks().forEach(track => combinedStream.addTrack(track));
            });
            return combinedStream;
          }
        } else {
          return await navigator.mediaDevices.getUserMedia(constraints);
        }

      case 'safari':
      default:
        // Safari: Standard approach, requires user gesture
        return await navigator.mediaDevices.getUserMedia(constraints);
    }
  } catch (error) {
    // Handle browser-specific errors
    console.error(`getUserMedia error in ${browserType}:`, error);
    throw error;
  }
};

// Browser-specific permission checking
const checkPermissionsWithBrowserStrategy = async () => {
  const browserType = getBrowserType();

  // Safari/iOS: Skip permission queries, use getUserMedia attempts
  if (browserType === 'safari' || !supportsPermissionAPI()) {
    return {
      camera: false,
      microphone: false,
      cameraState: 'prompt',
      microphoneState: 'prompt',
      requiresUserGesture: true,
      cleanup: () => {}
    };
  }

  // Chrome/Firefox/Edge: Use permission API
  try {
    const cameraPermission = await navigator.permissions.query({ name: 'camera' });
    const micPermission = await navigator.permissions.query({ name: 'microphone' });

    const handleCameraChange = () => {
      // Permission change handler will be set up in the calling function
    };

    const handleMicChange = () => {
      // Permission change handler will be set up in the calling function
    };

    cameraPermission.addEventListener('change', handleCameraChange);
    micPermission.addEventListener('change', handleMicChange);

    return {
      camera: cameraPermission.state === 'granted',
      microphone: micPermission.state === 'granted',
      cameraState: cameraPermission.state,
      microphoneState: micPermission.state,
      requiresUserGesture: false,
      cleanup: () => {
        cameraPermission.removeEventListener('change', handleCameraChange);
        micPermission.removeEventListener('change', handleMicChange);
      },
      cameraPermission,
      micPermission
    };
  } catch (error) {
    console.error('Permission API error:', error);
    return {
      camera: false,
      microphone: false,
      cameraState: 'denied',
      microphoneState: 'denied',
      requiresUserGesture: requiresUserGesture(),
      cleanup: () => {}
    };
  }
};

// Export the utilities
export {
  isMobileBrowser,
  getBrowserType,
  isSafari,
  isFirefox,
  supportsPermissionAPI,
  requiresUserGesture,
  getUserMediaWithBrowserStrategy,
  checkPermissionsWithBrowserStrategy
};

export default function PermissionUi({ open, onClose, onAllow, permissionType = 'both' }) {
  const getImage = () => {
    switch(permissionType) {
      case 'mic':
        return PermissionUiMicImage;
      case 'camera':
      case 'both':
      default:
        return PermissionUiImage;
    }
  };

  const getTitle = () => {
    switch(permissionType) {
      case 'mic':
        return "Enable your microphone for the meeting";
      case 'camera':
        return "Enable your camera for the meeting";
      case 'both':
      default:
        return "Enable your camera for the meeting";
    }
  };

  const getDescription = () => {
    switch(permissionType) {
      case 'mic':
        return "You can still turn it off anytime during the call";
      case 'camera':
      case 'both':
      default:
        return "You can still turn it off anytime during the call";
    }
  };

  return (
    <Modal
      open={open}
      onCancel={onClose}
      footer={null}
      centered
      width={600}
      closable={false}
      maskClosable={false}
      className="permission-ui-modal"
      bodyStyle={{ padding: 0, overflow: 'hidden', minHeight: '420px' }}
      style={{ maxWidth: '600px', height: 'auto' }}
      maskStyle={{
        backgroundColor: "rgba(0, 0, 0, 0.55)",
        backdropFilter: "blur(10px)",
      }}
    >
      <div className="permission-ui-container container text-center p-0">
        <img src={getImage()} alt="Enable camera and mic" className="permission-ui-illustration" />
        <h2 className="permission-ui-title mb-2">{getTitle()}</h2>
        <p className="permission-ui-desc mb-4">{getDescription()}</p>
        <div className="d-flex flex-column align-items-center w-100">
          {permissionType === 'both' && (
            <>
              <button className="btn btn-outline-primary w-100 permission-ui-btn" onClick={() => onAllow('mic_camera')}>Turn on Mic and Camera</button>
              <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
            </>
          )}
          {permissionType === 'camera' && (
            <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('camera')}>Turn on Camera</button>
          )}
          {permissionType === 'mic' && (
            <button className="btn btn-primary w-100 permission-ui-btn" onClick={() => onAllow('mic')}>Turn on Microphone</button>
          )}
          <button className="btn btn-link permission-ui-link" onClick={() => onAllow('skip')}>Continue without using microphone and camera</button>
        </div>
      </div>
    </Modal>
  );
}
